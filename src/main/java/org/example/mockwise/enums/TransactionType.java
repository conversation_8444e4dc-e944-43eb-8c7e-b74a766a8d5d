package org.example.mockwise.enums;

/**
 * Types of transactions in the system
 */
public enum TransactionType {
    DEPOSIT("Deposit"), // 
    WITHDRAWAL("Withdrawal"),
    CURRENCY_EXCHANGE("Currency Exchange"),
    MONEY_TRANSFER("Money Transfer"),
    FEE("Fee");

    private final String displayName;

    TransactionType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
