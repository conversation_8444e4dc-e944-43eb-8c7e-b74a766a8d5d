package org.example.mockwise.enums;

/**
 * Supported currencies in the system
 */
public enum Currency {
    USD("US Dollar", "$"),
    EUR("Euro", "€"),
    GBP("British Pound", "£"),
    JPY("Japanese Yen", "¥"),
    CAD("Canadian Dollar", "C$"),
    AUD("Australian Dollar", "A$"),
    CHF("Swiss Franc", "CHF"),
    CNY("Chinese Yuan", "¥"),
    SEK("Swedish Krona", "kr"),
    NOK("Norwegian Krone", "kr"),
    MXN("Mexican Peso", "$"),
    NZD("New Zealand Dollar", "NZ$"),
    SGD("Singapore Dollar", "S$"),
    HKD("Hong Kong Dollar", "HK$"),
    KRW("South Korean Won", "₩"),
    TRY("Turkish Lira", "₺"),
    RUB("Russian Ruble", "₽"),
    INR("Indian Rupee", "₹"),
    BRL("Brazilian Real", "R$"),
    ZAR("South African Rand", "R");

    private final String displayName;
    private final String symbol;

    Currency(String displayName, String symbol) {
        this.displayName = displayName;
        this.symbol = symbol;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getSymbol() {
        return symbol;
    }
}
