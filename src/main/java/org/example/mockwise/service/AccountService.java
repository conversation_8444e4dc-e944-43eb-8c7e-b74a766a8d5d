package org.example.mockwise.service;

import org.example.mockwise.entity.Account;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.repository.AccountRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing user accounts and balances
 */
@Service
@Transactional
public class AccountService {

    private static final Logger logger = LoggerFactory.getLogger(AccountService.class);

    @Autowired
    private AccountRepository accountRepository;

    /**
     * Get or create account for user and currency
     */
    public Account getOrCreateAccount(User user, Currency currency) {
        Optional<Account> existingAccount = accountRepository.findByUserAndCurrency(user, currency);
        
        if (existingAccount.isPresent()) {
            return existingAccount.get();
        }

        // Create new account with zero balance
        Account newAccount = new Account(user, currency);
        Account savedAccount = accountRepository.save(newAccount);
        logger.info("Created new {} account for user ID: {}", currency, user.getId());
        
        return savedAccount;
    }

    /**
     * Get account balance for user and currency
     */
    public BigDecimal getBalance(User user, Currency currency) {
        Optional<Account> account = accountRepository.findByUserAndCurrency(user, currency);
        return account.map(Account::getBalance).orElse(BigDecimal.ZERO);
    }

    /**
     * Get all accounts for a user
     */
    public List<Account> getUserAccounts(User user) {
        return accountRepository.findByUserOrderByCurrency(user);
    }

    /**
     * Deposit funds to user account
     */
    public Account deposit(User user, Currency currency, BigDecimal amount, String description) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Deposit amount must be positive");
        }

        Account account = getOrCreateAccount(user, currency);
        account.credit(amount);
        Account savedAccount = accountRepository.save(account);
        
        logger.info("Deposited {} {} to user ID: {} account. New balance: {}", 
                   amount, currency, user.getId(), savedAccount.getBalance());
        
        return savedAccount;
    }

    /**
     * Withdraw funds from user account
     */
    public Account withdraw(User user, Currency currency, BigDecimal amount, String description) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Withdrawal amount must be positive");
        }

        Account account = getOrCreateAccount(user, currency);
        
        if (!account.hasSufficientBalance(amount)) {
            throw new RuntimeException("Insufficient balance. Available: " + account.getBalance() + 
                                     " " + currency + ", Required: " + amount + " " + currency);
        }

        account.debit(amount);
        Account savedAccount = accountRepository.save(account);
        
        logger.info("Withdrew {} {} from user ID: {} account. New balance: {}", 
                   amount, currency, user.getId(), savedAccount.getBalance());
        
        return savedAccount;
    }

    /**
     * Transfer funds between accounts (same user, different currencies)
     */
    public void transferBetweenAccounts(User user, Currency fromCurrency, Currency toCurrency, 
                                      BigDecimal fromAmount, BigDecimal toAmount) {
        Account fromAccount = getOrCreateAccount(user, fromCurrency);
        Account toAccount = getOrCreateAccount(user, toCurrency);

        if (!fromAccount.hasSufficientBalance(fromAmount)) {
            throw new RuntimeException("Insufficient balance in " + fromCurrency + " account");
        }

        fromAccount.debit(fromAmount);
        toAccount.credit(toAmount);

        accountRepository.save(fromAccount);
        accountRepository.save(toAccount);

        logger.info("Transferred {} {} to {} {} for user ID: {}", 
                   fromAmount, fromCurrency, toAmount, toCurrency, user.getId());
    }

    /**
     * Check if user has sufficient balance
     */
    public boolean hasSufficientBalance(User user, Currency currency, BigDecimal amount) {
        BigDecimal balance = getBalance(user, currency);
        return balance.compareTo(amount) >= 0;
    }

    /**
     * Get total balance across all currencies (converted to base currency)
     */
    public BigDecimal getTotalBalanceInBaseCurrency(User user, Currency baseCurrency) {
        List<Account> accounts = getUserAccounts(user);
        BigDecimal totalBalance = BigDecimal.ZERO;

        for (Account account : accounts) {
            if (account.getCurrency() == baseCurrency) {
                totalBalance = totalBalance.add(account.getBalance());
            } else {
                // In a real implementation, you would convert using exchange rates
                // For now, we'll just add the balance as-is
                totalBalance = totalBalance.add(account.getBalance());
            }
        }

        return totalBalance;
    }

    /**
     * Freeze account (for compliance/security reasons)
     */
    public void freezeAccount(Long accountId) {
        Optional<Account> account = accountRepository.findById(accountId);
        if (account.isPresent()) {
            // In a real implementation, you would add a 'frozen' field to the Account entity
            logger.info("Account ID: {} has been frozen", accountId);
        }
    }

    /**
     * Get account by ID
     */
    public Optional<Account> getAccountById(Long accountId) {
        return accountRepository.findById(accountId);
    }
}
