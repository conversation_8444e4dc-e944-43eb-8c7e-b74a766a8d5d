package org.example.mockwise.service;

import org.example.mockwise.entity.ExchangeRate;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.repository.ExchangeRateRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for managing exchange rates
 */
@Service
@Transactional
public class ExchangeRateService {

    private static final Logger logger = LoggerFactory.getLogger(ExchangeRateService.class);
    private static final int RATE_CACHE_MINUTES = 5;

    @Autowired
    private ExchangeRateRepository exchangeRateRepository;

    @Value("${mockwise.exchange-rate.api-key}")
    private String apiKey;

    @Value("${mockwise.exchange-rate.base-url}")
    private String baseUrl;

    private final WebClient webClient;
    private final Map<String, ExchangeRate> rateCache = new ConcurrentHashMap<>();

    public ExchangeRateService() {
        this.webClient = WebClient.builder().build();
    }

    /**
     * Get exchange rate between two currencies
     */
    public BigDecimal getExchangeRate(Currency from, Currency to) {
        if (from == to) {
            return BigDecimal.ONE;
        }

        String cacheKey = from + "_" + to;
        ExchangeRate cachedRate = rateCache.get(cacheKey);

        // Check cache first
        if (cachedRate != null && !cachedRate.isExpired(RATE_CACHE_MINUTES)) {
            return cachedRate.getRate();
        }

        // Try to get from database
        Optional<ExchangeRate> dbRate = exchangeRateRepository.findLatestByBaseCurrencyAndTargetCurrency(from, to);
        if (dbRate.isPresent() && !dbRate.get().isExpired(RATE_CACHE_MINUTES)) {
            rateCache.put(cacheKey, dbRate.get());
            return dbRate.get().getRate();
        }

        // Fetch from external API
        BigDecimal rate = fetchRateFromExternalAPI(from, to);
        if (rate != null) {
            // Save to database
            ExchangeRate exchangeRate = new ExchangeRate(from, to, rate);
            exchangeRateRepository.save(exchangeRate);
            rateCache.put(cacheKey, exchangeRate);
            return rate;
        }

        // Fallback to mock rates if external API fails
        return getMockExchangeRate(from, to);
    }

    /**
     * Fetch rate from external API (simplified implementation)
     */
    private BigDecimal fetchRateFromExternalAPI(Currency from, Currency to) {
        try {
            logger.info("Fetching exchange rate from external API: {} to {}", from, to);
            
            // This is a simplified implementation
            // In a real application, you would integrate with a real exchange rate API
            // For now, we'll return null to fall back to mock rates
            return null;
            
        } catch (Exception e) {
            logger.error("Error fetching exchange rate from external API", e);
            return null;
        }
    }

    /**
     * Get mock exchange rates for testing purposes
     */
    private BigDecimal getMockExchangeRate(Currency from, Currency to) {
        logger.info("Using mock exchange rate: {} to {}", from, to);
        
        // Mock exchange rates (these would be real rates in production)
        Map<String, BigDecimal> mockRates = Map.of(
            "USD_EUR", new BigDecimal("0.85"),
            "EUR_USD", new BigDecimal("1.18"),
            "USD_GBP", new BigDecimal("0.73"),
            "GBP_USD", new BigDecimal("1.37"),
            "EUR_GBP", new BigDecimal("0.86"),
            "GBP_EUR", new BigDecimal("1.16"),
            "USD_JPY", new BigDecimal("110.00"),
            "JPY_USD", new BigDecimal("0.0091"),
            "USD_CAD", new BigDecimal("1.25"),
            "CAD_USD", new BigDecimal("0.80")
        );

        String key = from + "_" + to;
        BigDecimal rate = mockRates.get(key);
        
        if (rate == null) {
            // If direct rate not found, try inverse
            String inverseKey = to + "_" + from;
            BigDecimal inverseRate = mockRates.get(inverseKey);
            if (inverseRate != null) {
                rate = BigDecimal.ONE.divide(inverseRate, 8, RoundingMode.HALF_UP);
            } else {
                // Default fallback rate
                rate = new BigDecimal("1.00");
            }
        }

        // Save mock rate to database and cache
        ExchangeRate exchangeRate = new ExchangeRate(from, to, rate);
        exchangeRateRepository.save(exchangeRate);
        rateCache.put(from + "_" + to, exchangeRate);

        return rate;
    }

    /**
     * Convert amount from one currency to another
     */
    public BigDecimal convertAmount(BigDecimal amount, Currency from, Currency to) {
        BigDecimal rate = getExchangeRate(from, to);
        return amount.multiply(rate).setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * Calculate fee for currency exchange
     */
    public BigDecimal calculateExchangeFee(BigDecimal amount, Currency from, Currency to) {
        // Simple fee calculation: 0.5% of the amount
        BigDecimal feeRate = new BigDecimal("0.005");
        return amount.multiply(feeRate).setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * Clean up old exchange rates
     */
    public void cleanupOldRates() {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(24);
        exchangeRateRepository.deleteByCreatedAtBefore(cutoff);
        logger.info("Cleaned up exchange rates older than 24 hours");
    }

    /**
     * Get all supported currency pairs
     */
    public Map<String, BigDecimal> getAllCurrentRates() {
        Map<String, BigDecimal> rates = new ConcurrentHashMap<>();
        
        for (Currency from : Currency.values()) {
            for (Currency to : Currency.values()) {
                if (from != to) {
                    rates.put(from + "_" + to, getExchangeRate(from, to));
                }
            }
        }
        
        return rates;
    }
}
