package org.example.mockwise.service;

import org.example.mockwise.entity.Transaction;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.enums.TransactionStatus;
import org.example.mockwise.enums.TransactionType;
import org.example.mockwise.repository.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing transactions
 */
@Service
@Transactional
public class TransactionService {

    private static final Logger logger = LoggerFactory.getLogger(TransactionService.class);

    @Autowired
    private TransactionRepository transactionRepository;

    /**
     * Create a new transaction
     */
    public Transaction createTransaction(Transaction transaction) {
        logger.info("Creating new transaction for user ID: {}, type: {}", 
                   transaction.getUser().getId(), transaction.getTransactionType());
        
        Transaction savedTransaction = transactionRepository.save(transaction);
        logger.info("Transaction created with ID: {} and reference: {}", 
                   savedTransaction.getId(), savedTransaction.getReferenceNumber());
        
        return savedTransaction;
    }

    /**
     * Update transaction
     */
    public Transaction updateTransaction(Transaction transaction) {
        logger.info("Updating transaction ID: {}", transaction.getId());
        return transactionRepository.save(transaction);
    }

    /**
     * Get transaction by ID
     */
    public Optional<Transaction> getTransactionById(Long id) {
        return transactionRepository.findById(id);
    }

    /**
     * Get transaction by reference number
     */
    public Optional<Transaction> getTransactionByReference(String referenceNumber) {
        return transactionRepository.findByReferenceNumber(referenceNumber);
    }

    /**
     * Get user transactions with pagination
     */
    public Page<Transaction> getUserTransactions(User user, Pageable pageable) {
        return transactionRepository.findByUserOrderByCreatedAtDesc(user, pageable);
    }

    /**
     * Get user transactions by type
     */
    public List<Transaction> getUserTransactionsByType(User user, TransactionType type) {
        return transactionRepository.findByUserAndTransactionType(user, type);
    }

    /**
     * Get user transactions by status
     */
    public List<Transaction> getUserTransactionsByStatus(User user, TransactionStatus status) {
        return transactionRepository.findByUserAndStatus(user, status);
    }

    /**
     * Get user transactions by currency
     */
    public List<Transaction> getUserTransactionsByCurrency(User user, Currency currency) {
        return transactionRepository.findByUserAndSourceCurrency(user, currency);
    }

    /**
     * Get user transactions within date range
     */
    public List<Transaction> getUserTransactionsInDateRange(User user, LocalDateTime startDate, LocalDateTime endDate) {
        return transactionRepository.findByUserAndDateRange(user, startDate, endDate);
    }

    /**
     * Search transactions with multiple criteria
     */
    public Page<Transaction> searchTransactions(User user, TransactionType type, TransactionStatus status, 
                                              Currency currency, Pageable pageable) {
        return transactionRepository.findByUserAndCriteria(user, type, status, currency, pageable);
    }

    /**
     * Mark transaction as completed
     */
    public void markTransactionCompleted(Long transactionId) {
        Optional<Transaction> transaction = transactionRepository.findById(transactionId);
        if (transaction.isPresent()) {
            Transaction tx = transaction.get();
            tx.markAsCompleted();
            transactionRepository.save(tx);
            logger.info("Transaction ID: {} marked as completed", transactionId);
        }
    }

    /**
     * Mark transaction as failed
     */
    public void markTransactionFailed(Long transactionId) {
        Optional<Transaction> transaction = transactionRepository.findById(transactionId);
        if (transaction.isPresent()) {
            Transaction tx = transaction.get();
            tx.markAsFailed();
            transactionRepository.save(tx);
            logger.info("Transaction ID: {} marked as failed", transactionId);
        }
    }

    /**
     * Mark transaction as processing
     */
    public void markTransactionProcessing(Long transactionId) {
        Optional<Transaction> transaction = transactionRepository.findById(transactionId);
        if (transaction.isPresent()) {
            Transaction tx = transaction.get();
            tx.markAsProcessing();
            transactionRepository.save(tx);
            logger.info("Transaction ID: {} marked as processing", transactionId);
        }
    }

    /**
     * Get transaction count by status for user
     */
    public long getTransactionCountByStatus(User user, TransactionStatus status) {
        return transactionRepository.countByUserAndStatus(user, status);
    }

    /**
     * Get pending transactions older than specified time
     */
    public List<Transaction> getPendingTransactionsOlderThan(LocalDateTime cutoffTime) {
        return transactionRepository.findPendingTransactionsOlderThan(TransactionStatus.PENDING, cutoffTime);
    }

    /**
     * Process stuck transactions (for maintenance)
     */
    public void processStuckTransactions() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(1);
        List<Transaction> stuckTransactions = getPendingTransactionsOlderThan(cutoffTime);
        
        for (Transaction transaction : stuckTransactions) {
            logger.warn("Found stuck transaction ID: {}, marking as failed", transaction.getId());
            transaction.markAsFailed();
            transactionRepository.save(transaction);
        }
        
        if (!stuckTransactions.isEmpty()) {
            logger.info("Processed {} stuck transactions", stuckTransactions.size());
        }
    }

    /**
     * Get transaction statistics for user
     */
    public TransactionStats getUserTransactionStats(User user) {
        long totalTransactions = transactionRepository.countByUserAndStatus(user, null);
        long completedTransactions = transactionRepository.countByUserAndStatus(user, TransactionStatus.COMPLETED);
        long pendingTransactions = transactionRepository.countByUserAndStatus(user, TransactionStatus.PENDING);
        long failedTransactions = transactionRepository.countByUserAndStatus(user, TransactionStatus.FAILED);
        
        return new TransactionStats(totalTransactions, completedTransactions, pendingTransactions, failedTransactions);
    }

    /**
     * Inner class for transaction statistics
     */
    public static class TransactionStats {
        private final long total;
        private final long completed;
        private final long pending;
        private final long failed;

        public TransactionStats(long total, long completed, long pending, long failed) {
            this.total = total;
            this.completed = completed;
            this.pending = pending;
            this.failed = failed;
        }

        public long getTotal() { return total; }
        public long getCompleted() { return completed; }
        public long getPending() { return pending; }
        public long getFailed() { return failed; }
    }
}
