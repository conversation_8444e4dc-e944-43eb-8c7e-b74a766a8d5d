package org.example.mockwise.service;

import org.example.mockwise.entity.Recipient;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.repository.RecipientRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing recipients for money transfers
 */
@Service
@Transactional
public class RecipientService {

    private static final Logger logger = LoggerFactory.getLogger(RecipientService.class);

    @Autowired
    private RecipientRepository recipientRepository;

    /**
     * Create a new recipient
     */
    public Recipient createRecipient(Recipient recipient) {
        logger.info("Creating new recipient for user ID: {}", recipient.getUser().getId());
        
        // Validate recipient data
        validateRecipient(recipient);
        
        Recipient savedRecipient = recipientRepository.save(recipient);
        logger.info("Recipient created with ID: {} for user ID: {}", 
                   savedRecipient.getId(), savedRecipient.getUser().getId());
        
        return savedRecipient;
    }

    /**
     * Update recipient
     */
    public Recipient updateRecipient(Recipient recipient) {
        logger.info("Updating recipient ID: {}", recipient.getId());
        
        // Validate recipient data
        validateRecipient(recipient);
        
        return recipientRepository.save(recipient);
    }

    /**
     * Get recipient by ID
     */
    public Optional<Recipient> getRecipientById(Long id) {
        return recipientRepository.findById(id);
    }

    /**
     * Get recipient by ID and user (for security)
     */
    public Optional<Recipient> getRecipientByIdAndUser(Long id, User user) {
        Optional<Recipient> recipient = recipientRepository.findById(id);
        if (recipient.isPresent() && recipient.get().getUser().getId().equals(user.getId())) {
            return recipient;
        }
        return Optional.empty();
    }

    /**
     * Get all recipients for a user
     */
    public List<Recipient> getUserRecipients(User user) {
        return recipientRepository.findByUser(user);
    }

    /**
     * Get recipients by user and currency
     */
    public List<Recipient> getUserRecipientsByCurrency(User user, Currency currency) {
        return recipientRepository.findByUserAndCurrency(user, currency);
    }

    /**
     * Get recipients by user and country
     */
    public List<Recipient> getUserRecipientsByCountry(User user, String country) {
        return recipientRepository.findByUserAndCountry(user, country);
    }

    /**
     * Search recipients by name
     */
    public List<Recipient> searchRecipientsByName(User user, String name) {
        return recipientRepository.findByUserAndRecipientNameContaining(user, name);
    }

    /**
     * Search recipients by bank name
     */
    public List<Recipient> searchRecipientsByBankName(User user, String bankName) {
        return recipientRepository.findByUserAndBankNameContaining(user, bankName);
    }

    /**
     * Delete recipient
     */
    public void deleteRecipient(Long recipientId, User user) {
        Optional<Recipient> recipient = getRecipientByIdAndUser(recipientId, user);
        if (recipient.isPresent()) {
            recipientRepository.delete(recipient.get());
            logger.info("Deleted recipient ID: {} for user ID: {}", recipientId, user.getId());
        } else {
            throw new RuntimeException("Recipient not found or access denied");
        }
    }

    /**
     * Get recipient count for user
     */
    public long getRecipientCount(User user) {
        return recipientRepository.countByUser(user);
    }

    /**
     * Validate recipient data
     */
    private void validateRecipient(Recipient recipient) {
        // Basic validation
        if (recipient.getRecipientName() == null || recipient.getRecipientName().trim().isEmpty()) {
            throw new IllegalArgumentException("Recipient name is required");
        }

        if (recipient.getBankName() == null || recipient.getBankName().trim().isEmpty()) {
            throw new IllegalArgumentException("Bank name is required");
        }

        if (recipient.getAccountNumber() == null || recipient.getAccountNumber().trim().isEmpty()) {
            throw new IllegalArgumentException("Account number is required");
        }

        if (recipient.getCurrency() == null) {
            throw new IllegalArgumentException("Currency is required");
        }

        if (recipient.getCountry() == null || recipient.getCountry().trim().isEmpty()) {
            throw new IllegalArgumentException("Country is required");
        }

        // Currency-specific validation
        validateCurrencySpecificFields(recipient);
    }

    /**
     * Validate currency-specific fields
     */
    private void validateCurrencySpecificFields(Recipient recipient) {
        Currency currency = recipient.getCurrency();
        
        switch (currency) {
            case EUR:
                // For EUR transfers, IBAN is typically required
                if (recipient.getIban() == null || recipient.getIban().trim().isEmpty()) {
                    logger.warn("IBAN is recommended for EUR transfers");
                }
                break;
                
            case USD:
                // For USD transfers, routing number might be required for US banks
                if ("US".equalsIgnoreCase(recipient.getCountry()) && 
                    (recipient.getRoutingNumber() == null || recipient.getRoutingNumber().trim().isEmpty())) {
                    logger.warn("Routing number is recommended for US USD transfers");
                }
                break;
                
            case GBP:
                // For GBP transfers, sort code might be required
                break;
                
            default:
                // For other currencies, SWIFT/BIC is often required for international transfers
                if (recipient.getSwiftBic() == null || recipient.getSwiftBic().trim().isEmpty()) {
                    logger.warn("SWIFT/BIC code is recommended for international transfers");
                }
                break;
        }
    }

    /**
     * Check if recipient belongs to user
     */
    public boolean isRecipientOwnedByUser(Long recipientId, User user) {
        Optional<Recipient> recipient = recipientRepository.findById(recipientId);
        return recipient.isPresent() && recipient.get().getUser().getId().equals(user.getId());
    }

    /**
     * Get frequently used recipients (based on transfer history)
     */
    public List<Recipient> getFrequentlyUsedRecipients(User user, int limit) {
        // This would require joining with transfer/transaction data
        // For now, return all recipients ordered by creation date
        List<Recipient> allRecipients = getUserRecipients(user);
        return allRecipients.stream()
                           .limit(limit)
                           .toList();
    }
}
