package org.example.mockwise.service;

import org.example.mockwise.dto.ExchangeQuoteRequest;
import org.example.mockwise.dto.ExchangeQuoteResponse;
import org.example.mockwise.entity.Transaction;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.enums.TransactionStatus;
import org.example.mockwise.enums.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for currency exchange operations
 */
@Service
@Transactional
public class CurrencyExchangeService {

    private static final Logger logger = LoggerFactory.getLogger(CurrencyExchangeService.class);
    private static final int QUOTE_VALIDITY_MINUTES = 15;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private TransactionService transactionService;

    // Store quotes temporarily (in production, use Redis or similar)
    private final Map<String, ExchangeQuoteResponse> quoteCache = new ConcurrentHashMap<>();

    /**
     * Generate exchange quote
     */
    public ExchangeQuoteResponse generateQuote(ExchangeQuoteRequest request) {
        logger.info("Generating exchange quote: {} {} to {}", 
                   request.getAmount(), request.getSourceCurrency(), request.getTargetCurrency());

        // Validate request
        validateExchangeRequest(request);

        // Get current exchange rate
        BigDecimal exchangeRate = exchangeRateService.getExchangeRate(
            request.getSourceCurrency(), request.getTargetCurrency());

        // Calculate target amount
        BigDecimal targetAmount = request.getAmount().multiply(exchangeRate)
                                         .setScale(4, RoundingMode.HALF_UP);

        // Calculate fee
        BigDecimal feeAmount = exchangeRateService.calculateExchangeFee(
            request.getAmount(), request.getSourceCurrency(), request.getTargetCurrency());

        // Generate quote ID
        String quoteId = UUID.randomUUID().toString();

        // Create quote response
        ExchangeQuoteResponse quote = new ExchangeQuoteResponse(
            request.getSourceCurrency(),
            request.getTargetCurrency(),
            request.getAmount(),
            targetAmount,
            exchangeRate,
            feeAmount,
            request.getSourceCurrency(), // Fee charged in source currency
            quoteId
        );

        // Cache the quote
        quoteCache.put(quoteId, quote);

        logger.info("Generated quote ID: {} - {} {} = {} {} (rate: {}, fee: {})", 
                   quoteId, request.getAmount(), request.getSourceCurrency(),
                   targetAmount, request.getTargetCurrency(), exchangeRate, feeAmount);

        return quote;
    }

    /**
     * Execute currency exchange based on quote
     */
    public Transaction executeExchange(User user, String quoteId) {
        logger.info("Executing currency exchange for user ID: {} with quote ID: {}", user.getId(), quoteId);

        // Retrieve quote
        ExchangeQuoteResponse quote = quoteCache.get(quoteId);
        if (quote == null) {
            throw new RuntimeException("Quote not found or expired");
        }

        // Validate quote is still valid
        if (quote.getQuoteValidUntil().isBefore(java.time.LocalDateTime.now())) {
            quoteCache.remove(quoteId);
            throw new RuntimeException("Quote has expired");
        }

        // Check user has sufficient balance (including fee)
        BigDecimal totalRequired = quote.getSourceAmount().add(quote.getFeeAmount());
        if (!accountService.hasSufficientBalance(user, quote.getSourceCurrency(), totalRequired)) {
            throw new RuntimeException("Insufficient balance. Required: " + totalRequired + 
                                     " " + quote.getSourceCurrency());
        }

        try {
            // Create transaction record
            Transaction transaction = new Transaction(user, TransactionType.CURRENCY_EXCHANGE, 
                                                    quote.getSourceCurrency(), quote.getSourceAmount());
            transaction.setTargetCurrency(quote.getTargetCurrency());
            transaction.setTargetAmount(quote.getTargetAmount());
            transaction.setExchangeRate(quote.getExchangeRate());
            transaction.setFeeAmount(quote.getFeeAmount());
            transaction.setFeeCurrency(quote.getFeeCurrency());
            transaction.setDescription("Currency exchange: " + quote.getSourceCurrency() + 
                                     " to " + quote.getTargetCurrency());
            transaction.setStatus(TransactionStatus.PROCESSING);

            // Save transaction
            transaction = transactionService.createTransaction(transaction);

            // Execute the exchange
            performExchange(user, quote);

            // Mark transaction as completed
            transaction.markAsCompleted();
            transactionService.updateTransaction(transaction);

            // Remove quote from cache
            quoteCache.remove(quoteId);

            logger.info("Currency exchange completed successfully. Transaction ID: {}", transaction.getId());
            return transaction;

        } catch (Exception e) {
            logger.error("Error executing currency exchange", e);
            throw new RuntimeException("Failed to execute currency exchange: " + e.getMessage());
        }
    }

    /**
     * Perform the actual exchange (debit source, credit target)
     */
    private void performExchange(User user, ExchangeQuoteResponse quote) {
        // Debit source currency (amount + fee)
        BigDecimal totalDebit = quote.getSourceAmount().add(quote.getFeeAmount());
        accountService.withdraw(user, quote.getSourceCurrency(), totalDebit, 
                              "Currency exchange: " + quote.getSourceCurrency() + " to " + quote.getTargetCurrency());

        // Credit target currency
        accountService.deposit(user, quote.getTargetCurrency(), quote.getTargetAmount(),
                             "Currency exchange: " + quote.getSourceCurrency() + " to " + quote.getTargetCurrency());
    }

    /**
     * Get current exchange rates for display
     */
    public Map<String, BigDecimal> getCurrentRates() {
        return exchangeRateService.getAllCurrentRates();
    }

    /**
     * Get exchange rate between two currencies
     */
    public BigDecimal getExchangeRate(Currency from, Currency to) {
        return exchangeRateService.getExchangeRate(from, to);
    }

    /**
     * Validate exchange request
     */
    private void validateExchangeRequest(ExchangeQuoteRequest request) {
        if (request.getSourceCurrency() == request.getTargetCurrency()) {
            throw new IllegalArgumentException("Source and target currencies cannot be the same");
        }

        if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Exchange amount must be positive");
        }

        // Minimum exchange amount check
        BigDecimal minimumAmount = new BigDecimal("1.00");
        if (request.getAmount().compareTo(minimumAmount) < 0) {
            throw new IllegalArgumentException("Minimum exchange amount is " + minimumAmount);
        }

        // Maximum exchange amount check (for compliance)
        BigDecimal maximumAmount = new BigDecimal("50000.00");
        if (request.getAmount().compareTo(maximumAmount) > 0) {
            throw new IllegalArgumentException("Maximum exchange amount is " + maximumAmount + 
                                             " per transaction");
        }
    }

    /**
     * Clean up expired quotes
     */
    public void cleanupExpiredQuotes() {
        quoteCache.entrySet().removeIf(entry -> 
            entry.getValue().getQuoteValidUntil().isBefore(java.time.LocalDateTime.now()));
        logger.info("Cleaned up expired exchange quotes");
    }

    /**
     * Get quote by ID (for validation)
     */
    public ExchangeQuoteResponse getQuote(String quoteId) {
        return quoteCache.get(quoteId);
    }
}
