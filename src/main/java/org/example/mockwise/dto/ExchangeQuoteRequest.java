package org.example.mockwise.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import org.example.mockwise.enums.Currency;

import java.math.BigDecimal;

/**
 * DTO for currency exchange quote request
 */
public class ExchangeQuoteRequest {

    @NotNull(message = "Source currency is required")
    private Currency sourceCurrency;

    @NotNull(message = "Target currency is required")
    private Currency targetCurrency;

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;

    // Constructors
    public ExchangeQuoteRequest() {}

    public ExchangeQuoteRequest(Currency sourceCurrency, Currency targetCurrency, BigDecimal amount) {
        this.sourceCurrency = sourceCurrency;
        this.targetCurrency = targetCurrency;
        this.amount = amount;
    }

    // Getters and Setters
    public Currency getSourceCurrency() { return sourceCurrency; }
    public void setSourceCurrency(Currency sourceCurrency) { this.sourceCurrency = sourceCurrency; }

    public Currency getTargetCurrency() { return targetCurrency; }
    public void setTargetCurrency(Currency targetCurrency) { this.targetCurrency = targetCurrency; }

    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }
}
