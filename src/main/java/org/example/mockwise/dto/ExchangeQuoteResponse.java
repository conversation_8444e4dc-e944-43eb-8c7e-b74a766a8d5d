package org.example.mockwise.dto;

import org.example.mockwise.enums.Currency;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO for currency exchange quote response
 */
public class ExchangeQuoteResponse {

    private Currency sourceCurrency;
    private Currency targetCurrency;
    private BigDecimal sourceAmount;
    private BigDecimal targetAmount;
    private BigDecimal exchangeRate;
    private BigDecimal feeAmount;
    private Currency feeCurrency;
    private BigDecimal totalCost;
    private LocalDateTime quoteValidUntil;
    private String quoteId;

    // Constructors
    public ExchangeQuoteResponse() {}

    public ExchangeQuoteResponse(Currency sourceCurrency, Currency targetCurrency, BigDecimal sourceAmount,
                               BigDecimal targetAmount, BigDecimal exchangeRate, BigDecimal feeAmount,
                               Currency feeCurrency, String quoteId) {
        this.sourceCurrency = sourceCurrency;
        this.targetCurrency = targetCurrency;
        this.sourceAmount = sourceAmount;
        this.targetAmount = targetAmount;
        this.exchangeRate = exchangeRate;
        this.feeAmount = feeAmount;
        this.feeCurrency = feeCurrency;
        this.totalCost = sourceAmount.add(feeAmount);
        this.quoteId = quoteId;
        this.quoteValidUntil = LocalDateTime.now().plusMinutes(15); // Quote valid for 15 minutes
    }

    // Getters and Setters
    public Currency getSourceCurrency() { return sourceCurrency; }
    public void setSourceCurrency(Currency sourceCurrency) { this.sourceCurrency = sourceCurrency; }

    public Currency getTargetCurrency() { return targetCurrency; }
    public void setTargetCurrency(Currency targetCurrency) { this.targetCurrency = targetCurrency; }

    public BigDecimal getSourceAmount() { return sourceAmount; }
    public void setSourceAmount(BigDecimal sourceAmount) { this.sourceAmount = sourceAmount; }

    public BigDecimal getTargetAmount() { return targetAmount; }
    public void setTargetAmount(BigDecimal targetAmount) { this.targetAmount = targetAmount; }

    public BigDecimal getExchangeRate() { return exchangeRate; }
    public void setExchangeRate(BigDecimal exchangeRate) { this.exchangeRate = exchangeRate; }

    public BigDecimal getFeeAmount() { return feeAmount; }
    public void setFeeAmount(BigDecimal feeAmount) { this.feeAmount = feeAmount; }

    public Currency getFeeCurrency() { return feeCurrency; }
    public void setFeeCurrency(Currency feeCurrency) { this.feeCurrency = feeCurrency; }

    public BigDecimal getTotalCost() { return totalCost; }
    public void setTotalCost(BigDecimal totalCost) { this.totalCost = totalCost; }

    public LocalDateTime getQuoteValidUntil() { return quoteValidUntil; }
    public void setQuoteValidUntil(LocalDateTime quoteValidUntil) { this.quoteValidUntil = quoteValidUntil; }

    public String getQuoteId() { return quoteId; }
    public void setQuoteId(String quoteId) { this.quoteId = quoteId; }
}
