package org.example.mockwise.repository;

import org.example.mockwise.entity.ExchangeRate;
import org.example.mockwise.enums.Currency;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for ExchangeRate entity
 */
@Repository
public interface ExchangeRateRepository extends JpaRepository<ExchangeRate, Long> {

    /**
     * Find exchange rate by currency pair
     */
    Optional<ExchangeRate> findByBaseCurrencyAndTargetCurrency(Currency baseCurrency, Currency targetCurrency);

    /**
     * Find all rates for a base currency
     */
    List<ExchangeRate> findByBaseCurrency(Currency baseCurrency);

    /**
     * Find all rates for a target currency
     */
    List<ExchangeRate> findByTargetCurrency(Currency targetCurrency);

    /**
     * Find latest exchange rate by currency pair
     */
    @Query("SELECT e FROM ExchangeRate e WHERE e.baseCurrency = :baseCurrency " +
           "AND e.targetCurrency = :targetCurrency ORDER BY e.createdAt DESC")
    Optional<ExchangeRate> findLatestByBaseCurrencyAndTargetCurrency(@Param("baseCurrency") Currency baseCurrency,
                                                                    @Param("targetCurrency") Currency targetCurrency);

    /**
     * Find rates updated after specific time
     */
    @Query("SELECT e FROM ExchangeRate e WHERE e.updatedAt > :since OR " +
           "(e.updatedAt IS NULL AND e.createdAt > :since)")
    List<ExchangeRate> findRatesUpdatedAfter(@Param("since") LocalDateTime since);

    /**
     * Find stale rates (older than specified time)
     */
    @Query("SELECT e FROM ExchangeRate e WHERE e.createdAt < :cutoffTime")
    List<ExchangeRate> findStaleRates(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Delete rates older than specified time
     */
    void deleteByCreatedAtBefore(LocalDateTime cutoffTime);
}
