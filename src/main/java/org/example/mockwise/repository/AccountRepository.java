package org.example.mockwise.repository;

import org.example.mockwise.entity.Account;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Account entity
 */
@Repository
public interface AccountRepository extends JpaRepository<Account, Long> {

    /**
     * Find account by user and currency
     */
    Optional<Account> findByUserAndCurrency(User user, Currency currency);

    /**
     * Find all accounts for a user
     */
    List<Account> findByUser(User user);

    /**
     * Find all accounts for a user ordered by currency
     */
    List<Account> findByUserOrderByCurrency(User user);

    /**
     * Find accounts with balance greater than specified amount
     */
    @Query("SELECT a FROM Account a WHERE a.user = :user AND a.balance > :minBalance")
    List<Account> findByUserWithMinimumBalance(@Param("user") User user, 
                                              @Param("minBalance") BigDecimal minBalance);

    /**
     * Find accounts by currency
     */
    List<Account> findByCurrency(Currency currency);

    /**
     * Check if user has account with specific currency
     */
    boolean existsByUserAndCurrency(User user, Currency currency);

    /**
     * Get total balance for user in specific currency
     */
    @Query("SELECT COALESCE(SUM(a.balance), 0) FROM Account a WHERE a.user = :user AND a.currency = :currency")
    BigDecimal getTotalBalanceByUserAndCurrency(@Param("user") User user, @Param("currency") Currency currency);
}
