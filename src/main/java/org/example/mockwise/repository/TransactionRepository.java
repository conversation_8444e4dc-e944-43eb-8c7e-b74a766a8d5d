package org.example.mockwise.repository;

import org.example.mockwise.entity.Transaction;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.enums.TransactionStatus;
import org.example.mockwise.enums.TransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Transaction entity
 */
@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {

    /**
     * Find transaction by reference number
     */
    Optional<Transaction> findByReferenceNumber(String referenceNumber);

    /**
     * Find all transactions for a user with pagination
     */
    Page<Transaction> findByUserOrderByCreatedAtDesc(User user, Pageable pageable);

    /**
     * Find transactions by user and type
     */
    List<Transaction> findByUserAndTransactionType(User user, TransactionType transactionType);

    /**
     * Find transactions by user and status
     */
    List<Transaction> findByUserAndStatus(User user, TransactionStatus status);

    /**
     * Find transactions by user and currency
     */
    List<Transaction> findByUserAndSourceCurrency(User user, Currency currency);

    /**
     * Find transactions within date range
     */
    @Query("SELECT t FROM Transaction t WHERE t.user = :user AND " +
           "t.createdAt BETWEEN :startDate AND :endDate ORDER BY t.createdAt DESC")
    List<Transaction> findByUserAndDateRange(@Param("user") User user,
                                           @Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate);

    /**
     * Find transactions by multiple criteria
     */
    @Query("SELECT t FROM Transaction t WHERE t.user = :user " +
           "AND (:type IS NULL OR t.transactionType = :type) " +
           "AND (:status IS NULL OR t.status = :status) " +
           "AND (:currency IS NULL OR t.sourceCurrency = :currency) " +
           "ORDER BY t.createdAt DESC")
    Page<Transaction> findByUserAndCriteria(@Param("user") User user,
                                          @Param("type") TransactionType type,
                                          @Param("status") TransactionStatus status,
                                          @Param("currency") Currency currency,
                                          Pageable pageable);

    /**
     * Count transactions by user and status
     */
    long countByUserAndStatus(User user, TransactionStatus status);

    /**
     * Find pending transactions older than specified time
     */
    @Query("SELECT t FROM Transaction t WHERE t.status = :status AND t.createdAt < :cutoffTime")
    List<Transaction> findPendingTransactionsOlderThan(@Param("status") TransactionStatus status,
                                                      @Param("cutoffTime") LocalDateTime cutoffTime);
}
