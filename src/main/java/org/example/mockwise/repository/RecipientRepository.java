package org.example.mockwise.repository;

import org.example.mockwise.entity.Recipient;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for Recipient entity
 */
@Repository
public interface RecipientRepository extends JpaRepository<Recipient, Long> {

    /**
     * Find all recipients for a user
     */
    List<Recipient> findByUser(User user);

    /**
     * Find recipients by user and currency
     */
    List<Recipient> findByUserAndCurrency(User user, Currency currency);

    /**
     * Find recipients by user and country
     */
    List<Recipient> findByUserAndCountry(User user, String country);

    /**
     * Search recipients by name
     */
    @Query("SELECT r FROM Recipient r WHERE r.user = :user AND " +
           "LOWER(r.recipientName) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<Recipient> findByUserAndRecipientNameContaining(@Param("user") User user, 
                                                        @Param("name") String name);

    /**
     * Find recipients by bank name
     */
    @Query("SELECT r FROM Recipient r WHERE r.user = :user AND " +
           "LOWER(r.bankName) LIKE LOWER(CONCAT('%', :bankName, '%'))")
    List<Recipient> findByUserAndBankNameContaining(@Param("user") User user, 
                                                   @Param("bankName") String bankName);

    /**
     * Count recipients for a user
     */
    long countByUser(User user);
}
