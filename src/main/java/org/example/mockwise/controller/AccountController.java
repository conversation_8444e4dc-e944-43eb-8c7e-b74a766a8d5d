package org.example.mockwise.controller;

import org.example.mockwise.entity.Account;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.service.AccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller for account and balance management
 */
@RestController
@RequestMapping("/api/accounts")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AccountController {

    private static final Logger logger = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private AccountService accountService;

    /**
     * Get all user accounts
     */
    @GetMapping
    public ResponseEntity<?> getUserAccounts(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<Account> accounts = accountService.getUserAccounts(user);
            
            List<Map<String, Object>> accountData = accounts.stream()
                .map(this::convertAccountToMap)
                .collect(Collectors.toList());
            
            return ResponseEntity.ok(accountData);

        } catch (Exception e) {
            logger.error("Failed to get user accounts", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get accounts"));
        }
    }

    /**
     * Get balance for specific currency
     */
    @GetMapping("/balance/{currency}")
    public ResponseEntity<?> getBalance(@PathVariable Currency currency,
                                      Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            BigDecimal balance = accountService.getBalance(user, currency);
            
            Map<String, Object> response = new HashMap<>();
            response.put("currency", currency);
            response.put("balance", balance);
            response.put("currencySymbol", currency.getSymbol());
            response.put("currencyName", currency.getDisplayName());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get balance for currency: {}", currency, e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get balance"));
        }
    }

    /**
     * Get all balances
     */
    @GetMapping("/balances")
    public ResponseEntity<?> getAllBalances(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<Currency, BigDecimal> balances = new HashMap<>();
            
            // Get balances for all supported currencies
            for (Currency currency : Currency.values()) {
                BigDecimal balance = accountService.getBalance(user, currency);
                if (balance.compareTo(BigDecimal.ZERO) > 0) {
                    balances.put(currency, balance);
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("balances", balances);
            response.put("totalCurrencies", balances.size());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get all balances", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get balances"));
        }
    }

    /**
     * Deposit funds (simulated)
     */
    @PostMapping("/deposit")
    public ResponseEntity<?> deposit(@RequestParam Currency currency,
                                   @RequestParam BigDecimal amount,
                                   @RequestParam(required = false) String description,
                                   Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            logger.info("Deposit request: {} {} for user ID: {}", amount, currency, user.getId());

            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Deposit amount must be positive"));
            }

            Account account = accountService.deposit(user, currency, amount, 
                description != null ? description : "Deposit");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Deposit successful");
            response.put("currency", currency);
            response.put("amount", amount);
            response.put("newBalance", account.getBalance());
            response.put("timestamp", java.time.LocalDateTime.now());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to process deposit", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to process deposit: " + e.getMessage()));
        }
    }

    /**
     * Withdraw funds (simulated)
     */
    @PostMapping("/withdraw")
    public ResponseEntity<?> withdraw(@RequestParam Currency currency,
                                    @RequestParam BigDecimal amount,
                                    @RequestParam(required = false) String description,
                                    Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            logger.info("Withdrawal request: {} {} for user ID: {}", amount, currency, user.getId());

            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Withdrawal amount must be positive"));
            }

            Account account = accountService.withdraw(user, currency, amount, 
                description != null ? description : "Withdrawal");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Withdrawal successful");
            response.put("currency", currency);
            response.put("amount", amount);
            response.put("newBalance", account.getBalance());
            response.put("timestamp", java.time.LocalDateTime.now());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to process withdrawal", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to process withdrawal: " + e.getMessage()));
        }
    }

    /**
     * Check if user has sufficient balance
     */
    @GetMapping("/check-balance")
    public ResponseEntity<?> checkBalance(@RequestParam Currency currency,
                                        @RequestParam BigDecimal amount,
                                        Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            boolean hasSufficientBalance = accountService.hasSufficientBalance(user, currency, amount);
            BigDecimal currentBalance = accountService.getBalance(user, currency);
            
            Map<String, Object> response = new HashMap<>();
            response.put("currency", currency);
            response.put("requestedAmount", amount);
            response.put("currentBalance", currentBalance);
            response.put("hasSufficientBalance", hasSufficientBalance);
            response.put("shortfall", hasSufficientBalance ? BigDecimal.ZERO : amount.subtract(currentBalance));
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to check balance", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to check balance"));
        }
    }

    /**
     * Convert Account entity to Map for JSON response
     */
    private Map<String, Object> convertAccountToMap(Account account) {
        Map<String, Object> accountMap = new HashMap<>();
        accountMap.put("id", account.getId());
        accountMap.put("currency", account.getCurrency());
        accountMap.put("currencyName", account.getCurrency().getDisplayName());
        accountMap.put("currencySymbol", account.getCurrency().getSymbol());
        accountMap.put("balance", account.getBalance());
        accountMap.put("createdAt", account.getCreatedAt());
        accountMap.put("updatedAt", account.getUpdatedAt());
        return accountMap;
    }

    /**
     * Helper method to create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("timestamp", java.time.LocalDateTime.now());
        return response;
    }
}
