package org.example.mockwise.controller;

import jakarta.validation.Valid;
import org.example.mockwise.dto.ExchangeQuoteRequest;
import org.example.mockwise.dto.ExchangeQuoteResponse;
import org.example.mockwise.entity.Transaction;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.example.mockwise.service.CurrencyExchangeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Controller for currency exchange operations
 */
@RestController
@RequestMapping("/api/exchange")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ExchangeController {

    private static final Logger logger = LoggerFactory.getLogger(ExchangeController.class);

    @Autowired
    private CurrencyExchangeService currencyExchangeService;

    /**
     * Get current exchange rates
     */
    @GetMapping("/rates")
    public ResponseEntity<?> getCurrentRates() {
        try {
            Map<String, BigDecimal> rates = currencyExchangeService.getCurrentRates();
            return ResponseEntity.ok(rates);
        } catch (Exception e) {
            logger.error("Failed to get current exchange rates", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get exchange rates"));
        }
    }

    /**
     * Get exchange rate between two currencies
     */
    @GetMapping("/rate")
    public ResponseEntity<?> getExchangeRate(@RequestParam Currency from, @RequestParam Currency to) {
        try {
            BigDecimal rate = currencyExchangeService.getExchangeRate(from, to);
            
            Map<String, Object> response = new HashMap<>();
            response.put("from", from);
            response.put("to", to);
            response.put("rate", rate);
            response.put("timestamp", java.time.LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to get exchange rate from {} to {}", from, to, e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get exchange rate"));
        }
    }

    /**
     * Generate exchange quote
     */
    @PostMapping("/quote")
    public ResponseEntity<?> generateQuote(@Valid @RequestBody ExchangeQuoteRequest request,
                                         Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            logger.info("Generating exchange quote for user ID: {}", user.getId());

            ExchangeQuoteResponse quote = currencyExchangeService.generateQuote(request);
            return ResponseEntity.ok(quote);

        } catch (Exception e) {
            logger.error("Failed to generate exchange quote", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to generate quote: " + e.getMessage()));
        }
    }

    /**
     * Execute currency exchange
     */
    @PostMapping("/execute")
    public ResponseEntity<?> executeExchange(@RequestParam String quoteId,
                                           Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            logger.info("Executing exchange for user ID: {} with quote ID: {}", user.getId(), quoteId);

            Transaction transaction = currencyExchangeService.executeExchange(user, quoteId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Exchange executed successfully");
            response.put("transactionId", transaction.getId());
            response.put("referenceNumber", transaction.getReferenceNumber());
            response.put("status", transaction.getStatus());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to execute exchange", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to execute exchange: " + e.getMessage()));
        }
    }

    /**
     * Get quote details
     */
    @GetMapping("/quote/{quoteId}")
    public ResponseEntity<?> getQuote(@PathVariable String quoteId,
                                    Authentication authentication) {
        try {
            ExchangeQuoteResponse quote = currencyExchangeService.getQuote(quoteId);
            
            if (quote == null) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Quote not found or expired"));
            }
            
            return ResponseEntity.ok(quote);

        } catch (Exception e) {
            logger.error("Failed to get quote details", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get quote details"));
        }
    }

    /**
     * Get supported currencies
     */
    @GetMapping("/currencies")
    public ResponseEntity<?> getSupportedCurrencies() {
        try {
            Map<String, Object> currencies = new HashMap<>();
            
            for (Currency currency : Currency.values()) {
                Map<String, String> currencyInfo = new HashMap<>();
                currencyInfo.put("code", currency.name());
                currencyInfo.put("name", currency.getDisplayName());
                currencyInfo.put("symbol", currency.getSymbol());
                currencies.put(currency.name(), currencyInfo);
            }
            
            return ResponseEntity.ok(currencies);

        } catch (Exception e) {
            logger.error("Failed to get supported currencies", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get supported currencies"));
        }
    }

    /**
     * Calculate exchange preview (without creating quote)
     */
    @PostMapping("/preview")
    public ResponseEntity<?> getExchangePreview(@Valid @RequestBody ExchangeQuoteRequest request) {
        try {
            // This is similar to quote but doesn't create a cached quote
            ExchangeQuoteResponse preview = currencyExchangeService.generateQuote(request);
            
            // Remove quote ID to indicate this is just a preview
            preview.setQuoteId(null);
            
            return ResponseEntity.ok(preview);

        } catch (Exception e) {
            logger.error("Failed to generate exchange preview", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to generate preview: " + e.getMessage()));
        }
    }

    /**
     * Helper method to create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("timestamp", java.time.LocalDateTime.now());
        return response;
    }
}
