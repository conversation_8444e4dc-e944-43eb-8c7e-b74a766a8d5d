package org.example.mockwise.controller;

import jakarta.validation.Valid;
import org.example.mockwise.dto.AuthResponse;
import org.example.mockwise.dto.LoginRequest;
import org.example.mockwise.dto.UserRegistrationRequest;
import org.example.mockwise.entity.User;
import org.example.mockwise.security.JwtUtils;
import org.example.mockwise.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for authentication operations
 */
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtils jwtUtils;

    /**
     * User registration endpoint
     */
    @PostMapping("/register")
    public ResponseEntity<?> registerUser(@Valid @RequestBody UserRegistrationRequest request) {
        try {
            logger.info("Registration attempt for email: {}", request.getEmail());

            // Check if email already exists
            if (userService.existsByEmail(request.getEmail())) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Email is already in use!"));
            }

            // Register user
            User user = userService.registerUser(request);

            // Generate JWT token
            String jwt = jwtUtils.generateToken(user.getEmail());

            // Create response
            AuthResponse authResponse = new AuthResponse(
                jwt, 
                user.getId(), 
                user.getEmail(), 
                user.getFirstName(), 
                user.getLastName()
            );

            logger.info("User registered successfully: {}", user.getEmail());
            return ResponseEntity.ok(authResponse);

        } catch (Exception e) {
            logger.error("Registration failed for email: {}", request.getEmail(), e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Registration failed: " + e.getMessage()));
        }
    }

    /**
     * User login endpoint
     */
    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest request) {
        try {
            logger.info("Login attempt for email: {}", request.getEmail());

            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // Generate JWT token
            String jwt = jwtUtils.generateToken(authentication.getName());

            // Get user details
            User user = (User) authentication.getPrincipal();

            // Create response
            AuthResponse authResponse = new AuthResponse(
                jwt, 
                user.getId(), 
                user.getEmail(), 
                user.getFirstName(), 
                user.getLastName()
            );

            logger.info("User logged in successfully: {}", user.getEmail());
            return ResponseEntity.ok(authResponse);

        } catch (Exception e) {
            logger.error("Login failed for email: {}", request.getEmail(), e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Invalid email or password"));
        }
    }

    /**
     * Logout endpoint (client-side token removal)
     */
    @PostMapping("/logout")
    public ResponseEntity<?> logoutUser() {
        SecurityContextHolder.clearContext();
        return ResponseEntity.ok(createSuccessResponse("User logged out successfully"));
    }

    /**
     * Validate token endpoint
     */
    @GetMapping("/validate")
    public ResponseEntity<?> validateToken(@RequestHeader("Authorization") String token) {
        try {
            if (token != null && token.startsWith("Bearer ")) {
                String jwt = token.substring(7);
                if (jwtUtils.validateToken(jwt)) {
                    String email = jwtUtils.getUsernameFromToken(jwt);
                    User user = userService.findByEmail(email).orElse(null);
                    
                    if (user != null) {
                        Map<String, Object> response = new HashMap<>();
                        response.put("valid", true);
                        response.put("userId", user.getId());
                        response.put("email", user.getEmail());
                        response.put("firstName", user.getFirstName());
                        response.put("lastName", user.getLastName());
                        response.put("kycStatus", user.getKycStatus());
                        
                        return ResponseEntity.ok(response);
                    }
                }
            }
            
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Invalid token"));
                
        } catch (Exception e) {
            logger.error("Token validation failed", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Token validation failed"));
        }
    }

    /**
     * Get current user info
     */
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser(Authentication authentication) {
        try {
            if (authentication != null && authentication.isAuthenticated()) {
                User user = (User) authentication.getPrincipal();
                
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("email", user.getEmail());
                userInfo.put("firstName", user.getFirstName());
                userInfo.put("lastName", user.getLastName());
                userInfo.put("kycStatus", user.getKycStatus());
                userInfo.put("mfaEnabled", user.getMfaEnabled());
                userInfo.put("createdAt", user.getCreatedAt());
                
                return ResponseEntity.ok(userInfo);
            }
            
            return ResponseEntity.badRequest()
                .body(createErrorResponse("User not authenticated"));
                
        } catch (Exception e) {
            logger.error("Failed to get current user info", e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Failed to get user info"));
        }
    }

    /**
     * Helper method to create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return response;
    }

    /**
     * Helper method to create success response
     */
    private Map<String, Object> createSuccessResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        return response;
    }
}
