package org.example.mockwise.service;

import org.example.mockwise.dto.ExchangeQuoteRequest;
import org.example.mockwise.dto.ExchangeQuoteResponse;
import org.example.mockwise.entity.User;
import org.example.mockwise.enums.Currency;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
public class CurrencyExchangeServiceTest {

    @Autowired
    private CurrencyExchangeService currencyExchangeService;

    @Autowired
    private UserService userService;

    @Autowired
    private AccountService accountService;

    private User testUser;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setDateOfBirth(LocalDate.of(1990, 1, 1));
        testUser.setAddress("123 Test Street");
        testUser.setPhoneNumber("+**********");
        testUser = userService.updateUser(testUser);

        // Add some balance for testing
        accountService.deposit(testUser, Currency.USD, new BigDecimal("1000.00"), "Test deposit");
    }

    @Test
    void testGenerateQuote() {
        // Create exchange quote request
        ExchangeQuoteRequest request = new ExchangeQuoteRequest();
        request.setSourceCurrency(Currency.USD);
        request.setTargetCurrency(Currency.EUR);
        request.setAmount(new BigDecimal("100.00"));

        // Generate quote
        ExchangeQuoteResponse quote = currencyExchangeService.generateQuote(request);

        // Verify quote
        assertNotNull(quote);
        assertEquals(Currency.USD, quote.getSourceCurrency());
        assertEquals(Currency.EUR, quote.getTargetCurrency());
        assertEquals(new BigDecimal("100.00"), quote.getSourceAmount());
        assertNotNull(quote.getTargetAmount());
        assertNotNull(quote.getExchangeRate());
        assertNotNull(quote.getFeeAmount());
        assertNotNull(quote.getQuoteId());
        assertTrue(quote.getTargetAmount().compareTo(BigDecimal.ZERO) > 0);
        assertTrue(quote.getFeeAmount().compareTo(BigDecimal.ZERO) >= 0);
    }

    @Test
    void testExecuteExchange() {
        // Generate quote first
        ExchangeQuoteRequest request = new ExchangeQuoteRequest();
        request.setSourceCurrency(Currency.USD);
        request.setTargetCurrency(Currency.EUR);
        request.setAmount(new BigDecimal("100.00"));

        ExchangeQuoteResponse quote = currencyExchangeService.generateQuote(request);

        // Get initial balances
        BigDecimal initialUsdBalance = accountService.getBalance(testUser, Currency.USD);
        BigDecimal initialEurBalance = accountService.getBalance(testUser, Currency.EUR);

        // Execute exchange
        assertDoesNotThrow(() -> {
            currencyExchangeService.executeExchange(testUser, quote.getQuoteId());
        });

        // Verify balances changed
        BigDecimal finalUsdBalance = accountService.getBalance(testUser, Currency.USD);
        BigDecimal finalEurBalance = accountService.getBalance(testUser, Currency.EUR);

        // USD balance should decrease by amount + fee
        BigDecimal expectedUsdDecrease = quote.getSourceAmount().add(quote.getFeeAmount());
        assertEquals(initialUsdBalance.subtract(expectedUsdDecrease), finalUsdBalance);

        // EUR balance should increase by target amount
        assertEquals(initialEurBalance.add(quote.getTargetAmount()), finalEurBalance);
    }

    @Test
    void testInsufficientBalance() {
        // Create exchange quote request for more than available balance
        ExchangeQuoteRequest request = new ExchangeQuoteRequest();
        request.setSourceCurrency(Currency.USD);
        request.setTargetCurrency(Currency.EUR);
        request.setAmount(new BigDecimal("2000.00")); // More than the 1000 deposited

        ExchangeQuoteResponse quote = currencyExchangeService.generateQuote(request);

        // Attempt to execute exchange should fail
        assertThrows(RuntimeException.class, () -> {
            currencyExchangeService.executeExchange(testUser, quote.getQuoteId());
        });
    }

    @Test
    void testSameCurrencyExchange() {
        // Create exchange quote request with same source and target currency
        ExchangeQuoteRequest request = new ExchangeQuoteRequest();
        request.setSourceCurrency(Currency.USD);
        request.setTargetCurrency(Currency.USD);
        request.setAmount(new BigDecimal("100.00"));

        // Should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            currencyExchangeService.generateQuote(request);
        });
    }

    @Test
    void testNegativeAmount() {
        // Create exchange quote request with negative amount
        ExchangeQuoteRequest request = new ExchangeQuoteRequest();
        request.setSourceCurrency(Currency.USD);
        request.setTargetCurrency(Currency.EUR);
        request.setAmount(new BigDecimal("-100.00"));

        // Should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            currencyExchangeService.generateQuote(request);
        });
    }
}
