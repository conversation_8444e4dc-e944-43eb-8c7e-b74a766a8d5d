# Test Database Configuration (H2 in-memory)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA Configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# JWT Configuration for tests
mockwise.jwt.secret=testSecretKey123456789012345678901234567890
mockwise.jwt.expiration=86400000

# Exchange Rate API Configuration for tests
mockwise.exchange-rate.api-key=test-api-key
mockwise.exchange-rate.base-url=https://api.test.com

# Application Configuration
spring.application.name=MockWise-Test

# Logging
logging.level.org.example.mockwise=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
