# MockWise - Currency Exchange & International Money Transfer System

MockWise is a comprehensive currency exchange and international money transfer system built with Spring Boot, similar to Wise (formerly TransferWise). The system provides transparent, efficient, and secure currency exchange services with real-time exchange rates and international money transfer capabilities.

## Features

### Core Functionality
- **User Management**: Registration, authentication, profile management with KYC status tracking
- **Multi-Currency Accounts**: Support for 20+ major currencies with individual balance tracking
- **Real-Time Exchange Rates**: Live currency exchange rates with caching and fallback mechanisms
- **Currency Exchange**: Transparent fee calculation and secure exchange execution
- **International Money Transfers**: Recipient management and money transfer capabilities
- **Transaction History**: Comprehensive transaction tracking and search functionality

### Security & Compliance
- **JWT Authentication**: Secure token-based authentication
- **Password Encryption**: BCrypt password hashing
- **KYC/AML Support**: User verification status tracking
- **Audit Logging**: Comprehensive transaction and activity logging
- **Multi-Factor Authentication**: Optional MFA support

### Technical Features
- **RESTful API**: Clean, well-documented REST endpoints
- **Database Integration**: PostgreSQL with JPA/Hibernate
- **Error Handling**: Comprehensive error handling and validation
- **Testing**: Unit and integration tests
- **Scalable Architecture**: Modular design for easy scaling

## Technology Stack

- **Backend**: Spring Boot 3.5.0, Java 17
- **Security**: Spring Security, JWT
- **Database**: PostgreSQL (production), H2 (testing)
- **ORM**: Spring Data JPA, Hibernate
- **Build Tool**: Maven
- **Testing**: JUnit 5, Spring Boot Test

## Supported Currencies

USD, EUR, GBP, JPY, CAD, AUD, CHF, CNY, SEK, NOK, MXN, NZD, SGD, HKD, KRW, TRY, RUB, INR, BRL, ZAR

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/validate` - Token validation
- `GET /api/auth/me` - Get current user info

### Currency Exchange
- `GET /api/exchange/rates` - Get all current exchange rates
- `GET /api/exchange/rate` - Get specific currency pair rate
- `POST /api/exchange/quote` - Generate exchange quote
- `POST /api/exchange/execute` - Execute currency exchange
- `GET /api/exchange/currencies` - Get supported currencies

### Account Management
- `GET /api/accounts` - Get user accounts
- `GET /api/accounts/balance/{currency}` - Get specific currency balance
- `GET /api/accounts/balances` - Get all balances
- `POST /api/accounts/deposit` - Deposit funds (simulated)
- `POST /api/accounts/withdraw` - Withdraw funds (simulated)

## Getting Started

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+ (for production)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd MockWise
   ```

2. **Configure Database**
   Update `src/main/resources/application.properties`:
   ```properties
   spring.datasource.url=*****************************************
   spring.datasource.username=your_username
   spring.datasource.password=your_password
   ```

3. **Build the application**
   ```bash
   mvn clean install
   ```

4. **Run the application**
   ```bash
   mvn spring-boot:run
   ```

The application will start on `http://localhost:8080`

### Database Setup

Create a PostgreSQL database:
```sql
CREATE DATABASE mockwise;
CREATE USER mockwise_user WITH PASSWORD 'mockwise_password';
GRANT ALL PRIVILEGES ON DATABASE mockwise TO mockwise_user;
```

### Testing

Run tests with:
```bash
mvn test
```

## Usage Examples

### 1. User Registration
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "address": "123 Main St",
    "phoneNumber": "+1234567890"
  }'
```

### 2. Get Exchange Quote
```bash
curl -X POST http://localhost:8080/api/exchange/quote \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "sourceCurrency": "USD",
    "targetCurrency": "EUR",
    "amount": 100.00
  }'
```

### 3. Execute Exchange
```bash
curl -X POST http://localhost:8080/api/exchange/execute?quoteId=QUOTE_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Architecture

### Entity Relationships
- **User** → Multiple **Accounts** (one per currency)
- **User** → Multiple **Recipients** (for transfers)
- **User** → Multiple **Transactions** (exchange/transfer history)
- **ExchangeRate** → Currency pair rates with timestamps

### Service Layer
- **UserService**: User management and authentication
- **AccountService**: Multi-currency balance management
- **ExchangeRateService**: Rate fetching and caching
- **CurrencyExchangeService**: Exchange quote generation and execution
- **TransactionService**: Transaction history and tracking
- **RecipientService**: Recipient management for transfers

### Security
- JWT-based stateless authentication
- BCrypt password encryption
- Role-based access control ready
- Request validation and sanitization

## Configuration

Key configuration properties in `application.properties`:

```properties
# JWT Configuration
mockwise.jwt.secret=your-secret-key
mockwise.jwt.expiration=********

# Exchange Rate API
mockwise.exchange-rate.api-key=your-api-key
mockwise.exchange-rate.base-url=https://api.exchangerate-api.com/v4/latest
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
